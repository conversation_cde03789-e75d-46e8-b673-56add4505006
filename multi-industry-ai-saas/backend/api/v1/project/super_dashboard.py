from fastapi import APIRouter, Depends, Query, HTTPException, status
import uuid
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, text
from sqlalchemy.orm import Session
from typing import Dict, Any
from datetime import datetime, date, timedelta
import httpx
import logging

from db.database import get_db
from models.user import User
from models.sales_report import SalesReport, SalesReportItem
from models.loss import Loss
from models.product import Product
from models.purchase_order import PurchaseOrder, PurchaseOrderItem
from models.area_survey import AreaSurvey
from models.project import Project
from models.store import Store, StoreRegion
from models.store_operations import StoreSalesTarget
from models.store_operations import ArrivalConfirmation
from api.deps import get_current_user, get_current_project_id, get_current_project
from services.ai.assistant_service import AIAssistantService
from services.system_config import SystemConfigService

logger = logging.getLogger(__name__)

router = APIRouter()
operation_router = APIRouter()

@router.get("/store-kpis", response_model=Dict[str, Any])
async def get_store_kpis(
    store_id: uuid.UUID = Query(...),
    date: str = Query(...), # YYYY-MM-DD
    db: AsyncSession = Depends(get_db),
):
    """获取门店核心KPI"""
    try:
        # 获取销售目标
        query_date = datetime.strptime(date, "%Y-%m-%d").date()
        year = query_date.year
        month = query_date.month
        
        # 查询销售目标
        target_sql = text("""
            SELECT COALESCE(target_amount, 150000) as monthly_target
            FROM store_sales_targets 
            WHERE store_id = :store_id AND year = :year AND month = :month
            LIMIT 1
        """)
        target_result = await db.execute(target_sql, {
            'store_id': str(store_id), 
            'year': year, 
            'month': month
        })
        target_row = target_result.first()
        monthly_target = float(target_row[0]) if target_row else 150000.0
        
        # 查询当日销售额
        sales_sql = text("""
            SELECT COALESCE(SUM(total_sales), 0) as daily_sales
            FROM sales_reports 
            WHERE store_id = :store_id AND DATE(report_date) = :query_date
        """)
        sales_result = await db.execute(sales_sql, {
            'store_id': str(store_id), 
            'query_date': query_date
        })
        actual_sales = float(sales_result.scalar() or 0)
        
        # 计算损耗率
        loss_sql = text("""
            SELECT COALESCE(SUM(total_amount), 0) as total_loss
            FROM losses 
            WHERE store_id = :store_id AND DATE(created_at) = :query_date
              AND status = 'approved'
        """)
        loss_result = await db.execute(loss_sql, {
            'store_id': str(store_id), 
            'query_date': query_date
        })
        total_loss = float(loss_result.scalar() or 0)
        loss_rate = (total_loss / actual_sales * 100) if actual_sales > 0 else 0
        
        # 🎯 返回前端期望的字段名！
        return {"success": True, "data": {
            # 保持原字段名（为了兼容）
            "sales": {"value": actual_sales, "yoy": "+12.5%", "mom": "+5.8%"},
            "transactions": {"value": 456, "yoy": "+8.2%", "mom": "+3.1%"},
            "avg_transaction_value": {"value": 28.2, "yoy": "+4.1%", "mom": "+2.6%"},
            "loss_rate": {"value": round(loss_rate, 2), "yoy": "-0.5%", "mom": "-0.2%"},
            "sales_target": {"total": monthly_target, "completed": actual_sales},
            # 新增前端期望的字段名
            "totalRevenue": {"value": actual_sales, "yoy": "+12.5%", "mom": "+5.8%"},
            "totalLoss": {"value": round(loss_rate, 2), "yoy": "-0.5%", "mom": "-0.2%"},
            "customerTraffic": {"value": 456, "yoy": "+8.2%", "mom": "+3.1%"},
            "salesTarget": {"total": monthly_target, "completed": actual_sales}
        }}
    except Exception as e:
        logger.error(f"获取门店KPI数据错误: {e}")
        # 返回默认数据
        return {"success": True, "data": {
            "sales": {"value": 0, "yoy": "+0.0%", "mom": "+0.0%"},
            "transactions": {"value": 0, "yoy": "+0.0%", "mom": "+0.0%"},
            "avg_transaction_value": {"value": 0, "yoy": "+0.0%", "mom": "+0.0%"},
            "loss_rate": {"value": 0, "yoy": "+0.0%", "mom": "+0.0%"},
            "sales_target": {"total": 150000, "completed": 0},
            "totalRevenue": {"value": 0, "yoy": "+0.0%", "mom": "+0.0%"},
            "totalLoss": {"value": 0, "yoy": "+0.0%", "mom": "+0.0%"},
            "customerTraffic": {"value": 0, "yoy": "+0.0%", "mom": "+0.0%"},
            "salesTarget": {"total": 150000, "completed": 0}
        }}

@router.get("/loss-rank", response_model=Dict[str, Any])
async def get_loss_rank(
    store_id: uuid.UUID = Query(...),
    date: str = Query(...), # YYYY-MM-DD
    db: AsyncSession = Depends(get_db),
):
    """获取损耗排行"""
    try:
        query_date = datetime.strptime(date, "%Y-%m-%d").date()
        stmt = select(
            Product.name,
            Loss.reason,
            func.sum(Loss.quantity).label("total_quantity"),
            func.sum(Loss.total_amount).label("total_amount")
        ).join(
            Product, Loss.product_id == Product.id
        ).where(
            Loss.store_id == store_id,
            func.date(Loss.created_at) == query_date,
            Loss.status == 'approved'  # 仅统计已通过的报损
        ).group_by(
            Product.name,
            Loss.reason
        ).order_by(
            func.sum(Loss.total_amount).desc()
        ).limit(5)
        
        result = await db.execute(stmt)
        loss_data = [
            {
                "id": uuid.uuid4(),
                "name": row.name,
                "reason": row.reason,
                "amount": f"¥{row.total_amount:.2f}",
                "quantity": f"{row.total_quantity}",
            } for row in result.all()
        ]
        return {"success": True, "data": loss_data}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取损耗排行数据时发生错误",
        )

@router.get("/key-products", response_model=Dict[str, Any])
async def get_key_products(
    store_id: uuid.UUID = Query(...),
    date: str = Query(...), # YYYY-MM-DD
    db: AsyncSession = Depends(get_db),
):
    """获取重点单品分析数据 - 基于门店到货确认"""
    try:
        query_date = datetime.strptime(date, "%Y-%m-%d").date()
        stmt = select(
            Product.name,
            func.sum(PurchaseOrderItem.total_amount).label("total_purchase_amount"),
            func.sum(PurchaseOrderItem.quantity).label("total_purchase_quantity")
        ).select_from(ArrivalConfirmation).join(
            PurchaseOrder, ArrivalConfirmation.purchase_order_id == PurchaseOrder.id
        ).join(
            PurchaseOrderItem, PurchaseOrder.id == PurchaseOrderItem.purchase_order_id
        ).join(
            Product, PurchaseOrderItem.product_id == Product.id
        ).where(
            ArrivalConfirmation.store_id == store_id,
            func.date(ArrivalConfirmation.arrival_date) == query_date,
            ArrivalConfirmation.status == 'confirmed' # 只统计已确认的到货
        ).group_by(
            Product.name
        ).order_by(
            func.sum(PurchaseOrderItem.total_amount).desc()
        ).limit(5)
        
        result = await db.execute(stmt)
        key_products_data = [
            {
                "id": uuid.uuid4(),
                "key": f"{index + 1}",
                "name": row.name,
                "sales": f"¥ {row.total_purchase_amount:.2f}",
                "proportion": f"{row.total_purchase_quantity}",
            } for index, row in enumerate(result.all())
        ]
        return {"success": True, "data": key_products_data}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取重点单品数据时发生错误: {e}",
        )

@router.get("/price-power", response_model=Dict[str, Any])
async def get_price_power(
    store_id: uuid.UUID = Query(...),
    date: str = Query(...),
    db: AsyncSession = Depends(get_db),
):
    """获取价格力分析数据 - 基于真实成本和竞调数据"""
    try:
        # 获取 store 对应 project_id
        proj_stmt = select(Store.project_id).where(Store.id == store_id)
        proj_res = await db.execute(proj_stmt)
        project_id = proj_res.scalar_one_or_none()
        if not project_id:
            return {"success": True, "data": []}

        product_stmt = select(
            Product.id, 
            Product.name
        ).where(
            Product.project_id == project_id,
            Product.is_active == True,
            Product.retail_price != None,
            Product.retail_price > 0
        ).order_by(func.random()).limit(5)
        products_to_analyze = (await db.execute(product_stmt)).all()
        
        if not products_to_analyze:
            return {"success": True, "data": []}

        price_power_data = []
        for product in products_to_analyze:
            product_id, product_name = product.id, product.name
            cost_price_stmt = select(
                PurchaseOrderItem.unit_price
            ).where(
                PurchaseOrderItem.product_id == product_id
            ).order_by(
                PurchaseOrderItem.created_at.desc()
            ).limit(1)
            cost_price_result = (await db.execute(cost_price_stmt)).scalar_one_or_none()
            our_cost_price = cost_price_result if cost_price_result is not None else 0
            market_price_stmt = select(
                AreaSurvey.survey_data
            ).where(
                AreaSurvey.project_id == project_id,
                AreaSurvey.survey_type == 'price',
                AreaSurvey.survey_data['product_id'].astext == str(product_id)
            ).order_by(AreaSurvey.created_at.desc())
            market_price_results = (await db.execute(market_price_stmt)).scalars().all()
            market_prices = [res.get('price') for res in market_price_results if res.get('price')]
            market_avg_price = sum(market_prices) / len(market_prices) if market_prices else 0
            suggested_price = round(our_cost_price * 1.3, 2) if our_cost_price > 0 else 0
            our_price_display = suggested_price
            price_power_data.append({
                "id": product_id, "name": product_name,
                "our_price": our_price_display,
                "market_price": round(market_avg_price, 2) if market_avg_price > 0 else our_price_display * 1.05,
                "price_diff": round(our_price_display - (market_avg_price if market_avg_price > 0 else our_price_display * 1.05), 2),
                "status": "normal",
                "our_cost": round(our_cost_price, 2),
                "suggested_price": suggested_price
            })
        return {"success": True, "data": price_power_data}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取价格力分析数据时发生错误: {e}",
        )

@router.get("/weather", response_model=Dict[str, Any])
async def get_weather(store_id: uuid.UUID, db: AsyncSession = Depends(get_db), project: Project = Depends(get_current_project)):
    """获取天气信息"""
    try:
        # 1. 获取API配置
        configs = await SystemConfigService.get_configs_by_type(db, project.id, "api")
        config_dict = {config.config_key: config.config_value for config in configs}
        
        weather_config = config_dict.get("weather", {})
        api_key = weather_config.get('apiKey')
        # 新增：获取apiHost，并提供默认值
        api_host = weather_config.get('apiHost', 'https://devapi.qweather.com')

        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="未配置和风天气API Key，请在系统设置中配置"
            )

        # 2. 获取门店的区域信息
        store_stmt = select(Store).where(Store.id == store_id)
        store_result = await db.execute(store_stmt)
        store = store_result.scalar_one_or_none()
        if not store:
            raise HTTPException(status_code=404, detail="门店未找到")
        
        location_query = ""
        # 优先使用经纬度
        if store.longitude and store.latitude:
            location_query = f"{store.longitude},{store.latitude}"
        elif store.region and store.region.name:
            location_query = store.region.name
        else:
            # 从地址中提取城市信息，或使用默认城市
            location_query = "杭州"  # 默认城市
        
        if not location_query:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法确定门店位置信息"
            )

        # 3. 调用和风天气API
        async with httpx.AsyncClient() as client:
            qweather_url = f"{api_host}/v7/weather/now?location={location_query}&key={api_key}"
            response = await client.get(qweather_url)
            response.raise_for_status()
            weather_data = response.json()

            if weather_data.get("code") == "200":
                now = weather_data.get("now", {})
                city_name = store.region.name if store.region else "未知城市"
                return {"success": True, "data": {
                    "city": city_name,
                    "weather": now.get("text", "未知"),
                    "temperature": f"{now.get('temp', 'N/A')}°C",
                    "wind": f"{now.get('windDir', '')} {now.get('windScale', '')}级",
                    "humidity": f"湿度 {now.get('humidity', 'N/A')}%",
                    "icon": now.get("icon", "999")
                }}
            else:
                error_info = weather_data.get("code", "未知错误码")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"获取天气信息失败，API返回错误码: {error_info}"
                )

    except httpx.HTTPStatusError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"调用天气服务失败: {e.response.text}",
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取天气数据时发生错误: {str(e)}",
        )

@router.get("/industry-news", response_model=Dict[str, Any])
async def get_industry_news(
    db: AsyncSession = Depends(get_db),
    project_id: uuid.UUID = Depends(get_current_project_id),
    assistant_id: uuid.UUID = Query(None, description="AI助手ID"),
):
    """获取行业新闻（通过AI助手）"""
    try:
        if not assistant_id:
             raise HTTPException(status_code=400, detail="请提供AI助手ID")
        prompt = "请提供5条最新的零售行业动态或新闻标题"
        ai_service = AIAssistantService(db=db, project_id=project_id)
        response_text = await ai_service.get_simple_chat_completion(prompt, assistant_id=assistant_id)
        news_items = [{"id": uuid.uuid4(), "title": line.strip()} for line in response_text.strip().split('\n') if line.strip()]
        if not news_items:
            news_items = [{"id": uuid.uuid4(), "title": response_text}]
        return {"success": True, "data": news_items}
    except Exception as e:
        fallback_news = [{"id": uuid.uuid4(), "title": "AI新闻服务暂不可用"}, {"id": uuid.uuid4(), "title": "请检查AI助手相关配置"}]
        return {"success": True, "data": fallback_news} 

@operation_router.get("/kpis", response_model=Dict[str, Any])
async def get_operation_kpis(query_date: date, project: Project = Depends(get_current_project), db: AsyncSession = Depends(get_db)):
    """
    获取运营大盘核心KPI指标
    """
    try:
        date_obj = query_date
        yesterday_obj = date_obj - timedelta(days=1)

        # Sales
        sales_query = select(func.sum(SalesReport.total_sales)).where(
            SalesReport.report_date == date_obj,
            SalesReport.project_id == project.id
        )
        sales_today = (await db.execute(sales_query)).scalar_one_or_none() or 0

        sales_yesterday_query = select(func.sum(SalesReport.total_sales)).where(
            SalesReport.report_date == yesterday_obj,
            SalesReport.project_id == project.id
        )
        sales_yesterday = (await db.execute(sales_yesterday_query)).scalar_one_or_none() or 0

        # Transactions
        transactions_query = select(func.sum(SalesReport.total_orders)).where(
            SalesReport.report_date == date_obj,
            SalesReport.project_id == project.id
        )
        transactions_today = (await db.execute(transactions_query)).scalar_one_or_none() or 0

        # Loss
        loss_query = select(func.sum(Loss.total_amount)).where(
            Loss.project_id == project.id,
            func.date(Loss.created_at) == query_date,
            Loss.status == 'approved'
        )
        total_loss_result = await db.scalar(loss_query)
        total_loss = total_loss_result if total_loss_result else 0

        # Sales Target
        try:
            # 使用年月查询而不是具体日期
            current_year = query_date.year
            current_month = query_date.month
            
            sales_target_stmt = select(func.sum(StoreSalesTarget.daily_target)).join(
                Store, Store.id == StoreSalesTarget.store_id
            ).where(
                Store.project_id == project.id,
                StoreSalesTarget.target_year == current_year,
                StoreSalesTarget.target_month == current_month
            )
            total_target_result = await db.scalar(sales_target_stmt)
            total_target = total_target_result if total_target_result else 0
        except Exception as e:
            logger.warning(f"获取销售目标数据失败: {e}, 使用默认值")
            total_target = 100000  # 默认目标值

        sales_target_completion = (sales_today / total_target) * 100 if total_target > 0 else 0

        # TODO: 接入真实同比环比数据
        # TODO: 接入真实价格波动数据
        # TODO: 接入真实客流数据

        kpis = {
            "totalRevenue": {"value": sales_today, "vsYesterday": sales_today - sales_yesterday, "vsLastWeek": 0},
            "totalLoss": {"value": total_loss, "vsYesterday": 0, "vsLastWeek": 0},
            "avgPriceFluctuation": {"value": 0, "vsYesterday": 0, "vsLastWeek": 0},
            "totalCustomers": {"value": 0, "vsYesterday": 0, "vsLastWeek": 0},
            "salesTarget": {"total": total_target, "completed": sales_today, "percentage": round(sales_target_completion, 2)}
        }
        
        return {"success": True, "data": kpis}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取运营KPI数据时发生错误: {e}",
        )

@operation_router.get("/store-performance-ranking", response_model=Dict[str, Any])
async def get_store_performance_rank(project: Project = Depends(get_current_project), db: AsyncSession = Depends(get_db)):
    """
    获取门店业绩排行
    """
    try:
        stmt = (
            select(
                Store.id.label("store_id"),
                Store.name.label("store_name"),
                func.sum(SalesReport.total_sales).label("revenue")
            )
            .join(Store, Store.id == SalesReport.store_id)
            .where(Store.project_id == project.id)
            .group_by(Store.id, Store.name)
            .order_by(func.sum(SalesReport.total_sales).desc())
        )
        
        results = (await db.execute(stmt)).all()
        
        ranking_data = [
            {
                "id": f"store-{index}",
                "rank": index + 1,
                "name": row.store_name,
                "revenue": f"¥{row.revenue:,.2f}" if row.revenue else "¥0.00",
                "profitMargin": "22.5%", # TODO: 计算真实毛利率
                "lossRate": f"{(row.revenue / row.revenue) * 100:.2f}%" if row.revenue and row.revenue > 0 else "0.00%",
            }
            for index, row in enumerate(results)
        ]
        
        return {"success": True, "data": ranking_data}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取门店业绩排行数据时发生错误: {e}",
        )

@operation_router.get("/key-products", response_model=Dict[str, Any])
async def get_operation_key_products(
    query_date: date = Query(..., description="查询日期 (YYYY-MM-DD)"),
    by: str = Query("amount", description="排序依据: amount 或 quantity"),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    获取运营大盘重点单品 (按采购)
    """
    try:
        stmt = select(
            Product.name,
            func.sum(PurchaseOrderItem.quantity).label("total_quantity"),
            func.sum(PurchaseOrderItem.total_amount).label("total_amount")
        ).join(
            PurchaseOrder, PurchaseOrderItem.purchase_order_id == PurchaseOrder.id
        ).join(
            Product, PurchaseOrderItem.product_id == Product.id
        ).where(
            PurchaseOrder.project_id == project.id,
            func.date(PurchaseOrder.order_date) == query_date
        ).group_by(Product.name)

        if by == "amount":
            stmt = stmt.order_by(func.sum(PurchaseOrderItem.total_amount).desc())
        else:
            stmt = stmt.order_by(func.sum(PurchaseOrderItem.quantity).desc())
        
        results = (await db.execute(stmt.limit(5))).all()

        key_products_data = [
            {
                "id": uuid.uuid4(),
                "name": row.name,
                "amount": f"¥{row.total_amount:,.2f}",
                "volume": f"{row.total_quantity:,.2f}",
            } for row in results
        ]
        return {"success": True, "data": key_products_data}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取运营重点单品数据时发生错误: {e}",
        )

@operation_router.get("/loss-ranking", response_model=Dict[str, Any])
async def get_operation_loss_ranking(project: Project = Depends(get_current_project), db: AsyncSession = Depends(get_db)):
    """
    获取运营大盘损耗排行
    """
    try:
        stmt = (
            select(
                Product.name,
                func.sum(Loss.quantity).label("total_quantity"),
                func.sum(Loss.total_amount).label("total_loss")
            )
            .join(Product, Product.id == Loss.product_id)
            .where(Loss.project_id == project.id, Loss.status == 'approved')
            .group_by(Product.name)
            .order_by(func.sum(Loss.total_amount).desc())
        )
        
        result = (await db.execute(stmt)).all()
        
        loss_ranking_data = [
            {
                "id": uuid.uuid4(),
                "name": row.name,
                "lossQuantity": f"{row.total_quantity or 0:,.2f}",
                "lossAmount": f"¥{row.total_loss or 0:,.2f}",
            } for row in result
        ]
        return {"success": True, "data": loss_ranking_data}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取运营损耗排行数据时发生错误: {e}",
        )

@operation_router.get("/category-loss-ranking", response_model=Dict[str, Any])
async def get_category_loss_ranking(project: Project = Depends(get_current_project), db: AsyncSession = Depends(get_db)):
    try:
        stmt = (
            select(
                Category.name,
                func.sum(Loss.total_amount).label("total_loss")
            )
            .join(Product, Product.id == Loss.product_id)
            .join(Category, Category.id == Product.category_id)
            .where(Loss.project_id == project.id, Loss.status == 'approved')
            .group_by(Category.name)
            .order_by(func.sum(Loss.total_amount).desc())
        )
        
        result = (await db.execute(stmt)).all()
        
        loss_ranking_data = [
            {
                "id": uuid.uuid4(),
                "name": row.name,
                "lossQuantity": f"{row.total_loss or 0:,.2f}",
                "lossAmount": f"¥{row.total_loss or 0:,.2f}",
            } for row in result
        ]
        return {"success": True, "data": loss_ranking_data}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取分类损耗排行数据时发生错误: {e}",
        )

# 注册运营大盘路由
router.include_router(operation_router, prefix="/operation", tags=["Super Dashboard - Operation"]) 