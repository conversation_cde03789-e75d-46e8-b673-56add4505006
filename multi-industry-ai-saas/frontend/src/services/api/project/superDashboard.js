import api, { getProjectId } from '../httpClient';

const BASE_URL = '/project';

const superDashboard = {
  getStoreKpis: (storeId, date) => {
    const projectId = getProjectId();
    return api.get(`${BASE_URL}/${projectId}/super-dashboard/store-kpis`, {
      params: { store_id: storeId, date },
    });
  },

  getPricePower: (storeId, date) => {
    const projectId = getProjectId();
    return api.get(`${BASE_URL}/${projectId}/super-dashboard/price-power`, {
      params: { store_id: storeId, date },
    });
  },

  getLossRank: (storeId, date) => {
    const projectId = getProjectId();
    return api.get(`${BASE_URL}/${projectId}/super-dashboard/loss-rank`, {
      params: { store_id: storeId, date },
    });
  },

  getKeyProducts: (storeId, date) => {
    const projectId = getProjectId();
    return api.get(`${BASE_URL}/${projectId}/super-dashboard/key-products`, {
      params: { store_id: storeId, date },
    });
  },

  getWeather: (storeId) => {
    const projectId = getProjectId();
    // 假设后端会根据store_id自动查询城市
    return api.get(`${BASE_URL}/${projectId}/super-dashboard/weather`, {
        params: { store_id: storeId },
    });
  },

  getIndustryNews: () => {
    const projectId = getProjectId();
    return api.get(`${BASE_URL}/${projectId}/super-dashboard/industry-news`);
  },

  // 新增运营大盘相关接口
  operation: {
    getKpis(date) {
      const projectId = getProjectId();
      return api.get(`${BASE_URL}/${projectId}/super-dashboard/operation/kpis`, { 
        params: { query_date: date } 
      });
    },
    getStorePerformanceRanking(date) {
      const projectId = getProjectId();
      return api.get(`${BASE_URL}/${projectId}/super-dashboard/operation/store-performance-ranking`, { 
        params: { query_date: date } 
      });
    },
    getKeyProducts(date, by = 'amount') {
      const projectId = getProjectId();
      return api.get(`${BASE_URL}/${projectId}/super-dashboard/operation/key-products`, { 
        params: { query_date: date, by } 
      });
    },
    getLossRanking(date) {
      const projectId = getProjectId();
      return api.get(`${BASE_URL}/${projectId}/super-dashboard/operation/loss-ranking`, { 
        params: { query_date: date } 
      });
    }
  },

  getNews: () => {
    const projectId = getProjectId();
    return api.get(`${BASE_URL}/${projectId}/super-dashboard/industry-news`);
  }
};

export default superDashboard; 