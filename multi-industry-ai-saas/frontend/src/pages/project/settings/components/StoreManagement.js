import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input, Select,
  Popconfirm, message, Tabs, Tag, Row, Col
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  EnvironmentOutlined, ShopOutlined, AppstoreOutlined
} from '@ant-design/icons';
import { project } from '../../../../services';
import userApi from '../../../../services/api/project/user';

const { Option } = Select;

/**
 * 门店管理组件
 * 用于管理门店、门店分类和区域
 */
const StoreManagement = () => {
  const [activeTab, setActiveTab] = useState('stores');
  const [storeModalOpen, setStoreModalOpen] = useState(false);
  const [categoryModalOpen, setCategoryModalOpen] = useState(false);
  const [regionModalOpen, setRegionModalOpen] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [storeForm] = Form.useForm();
  const [categoryForm] = Form.useForm();
  const [regionForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [stores, setStores] = useState([]);
  const [categories, setCategories] = useState([]);
  const [regions, setRegions] = useState([]);
  const [storeAdmins, setStoreAdmins] = useState([]);
  const [geocoding, setGeocoding] = useState(false);

  // 获取门店数据
  const fetchStores = async () => {
    setLoading(true);

    try {
      // 调用API获取门店列表
      const response = await project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      } else {
        message.error('获取门店列表失败');
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败: ' + (error.message || '未知错误'));
      setStores([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取门店分类数据
  const fetchCategories = async () => {
    try {
      const response = await project.store.getCategories();
      if (Array.isArray(response)) {
        setCategories(response);
      } else {
        setCategories([]);
        message.error('获取门店分类失败');
      }
    } catch (error) {
      setCategories([]);
      message.error('获取门店分类失败');
    }
  };

  // 获取门店区域数据
  const fetchRegions = async () => {
    try {
      const response = await project.store.getRegions();
      if (Array.isArray(response)) {
        setRegions(response);
      } else {
        setRegions([]);
        message.error('获取门店区域失败');
      }
    } catch (error) {
      setRegions([]);
      message.error('获取门店区域失败');
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchStores();
    fetchCategories();
    fetchRegions();
    userApi.getList().then(res => {
      let users = Array.isArray(res) ? res : (res.data || res.items || []);
      users = users.map(u => u.data || u);
      setStoreAdmins(users.filter(u => u.role === 'store_admin'));
    });
  }, []);

  // 门店表格列定义
  const storeColumns = [
    {
      title: '门店名称',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <a>{text}</a>
    },
    {
      title: '门店编码',
      dataIndex: 'code',
      key: 'code'
    },
    {
      title: '门店分类',
      dataIndex: 'category',
      key: 'category',
      render: (category) => category?.name || '-'
    },
    {
      title: '所属区域',
      dataIndex: 'region',
      key: 'region',
      render: (region) => region?.name || '-'
    },
    {
      title: '门店地址',
      dataIndex: 'address',
      key: 'address',
      ellipsis: true
    },
    {
      title: '负责人',
      key: 'manager_user_id',
      render: (_, record) => {
        const manager = storeAdmins.find(admin => admin.id === record.manager_user_id);
        return manager ? (manager.name || manager.username) : (record.manager || '-');
      }
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      key: 'phone'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '营业中' : '已关闭'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => showEditStoreModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除该门店吗？"
            onConfirm={() => handleDeleteStore(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 分类表格列定义
  const categoryColumns = [
    {
      title: '分类名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => showEditCategoryModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除该分类吗？"
            onConfirm={() => handleDeleteCategory(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 区域表格列定义
  const regionColumns = [
    {
      title: '区域名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '区域编码',
      dataIndex: 'code',
      key: 'code'
    },
    {
      title: '上级区域',
      dataIndex: 'parent_id',
      key: 'parent_id',
      render: (parentId) => {
        if (!parentId) return '-';
        const parentRegion = regions.find(r => r.id === parentId);
        return parentRegion ? parentRegion.name : '-';
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => showEditRegionModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除该区域吗？"
            onConfirm={() => handleDeleteRegion(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 显示创建门店模态框
  const showCreateStoreModal = () => {
    setEditingItem(null);
    storeForm.resetFields();
    setStoreModalOpen(true);
  };

  // 显示编辑门店模态框
  const showEditStoreModal = async (record) => {
    setLoading(true);
    try {
      // 先获取详情
      const detailedStore = await project.store.getDetail(record.id);
      if (!detailedStore) {
        message.error('获取门店详情失败');
        setLoading(false);
        return;
      }
      console.log('获取到的门店详情:', detailedStore);
      setEditingItem(detailedStore);

      // 确保有足够的门店管理员数据
      let currentStoreAdmins = storeAdmins;
      if (currentStoreAdmins.length === 0) {
        console.log('门店管理员数据为空，重新获取用户列表');
        const res = await userApi.getList();
        let users = Array.isArray(res) ? res : (res.data || res.items || []);
        users = users.map(u => u.data || u);
        currentStoreAdmins = users.filter(u => u.role === 'store_admin');
        console.log('重新获取的门店管理员:', currentStoreAdmins);
        setStoreAdmins(currentStoreAdmins); // 更新 state
      } else {
        console.log('使用现有门店管理员数据:', currentStoreAdmins.length, '个用户');
      }

      // 在 Modal 打开前设置表单值
      const formValues = {
        name: detailedStore.name,
        code: detailedStore.code,
        address: detailedStore.address,
        longitude: detailedStore.longitude,
        latitude: detailedStore.latitude,
        category_id: detailedStore.category_id,
        region_id: detailedStore.region_id,
        manager: detailedStore.manager_user_id ? String(detailedStore.manager_user_id) : undefined,
        phone: detailedStore.phone,
        status: detailedStore.status
      };

      console.log('设置表单值:', formValues);
      console.log('当前manager_user_id:', detailedStore.manager_user_id);
      console.log('可选的管理员:', currentStoreAdmins.map(admin => ({
        id: admin.id,
        name: admin.name || admin.username
      })));

      // 先设置表单值
        storeForm.setFieldsValue(formValues);
      
      // 然后打开 Modal
      setStoreModalOpen(true);

    } catch (error) {
      console.error('获取门店详情失败:', error);
      message.error('获取门店详情失败: ' + (error.message || '未知错误'));
    } finally {
      // 确保 loading 状态在所有操作后关闭
      requestAnimationFrame(() => {
         setLoading(false);
      });
    }
  };

  // 显示创建分类模态框
  const showCreateCategoryModal = () => {
    setEditingItem(null);
    categoryForm.resetFields();
    setCategoryModalOpen(true);
  };

  // 显示编辑分类模态框
  const showEditCategoryModal = (category) => {
    setEditingItem(category);
    categoryForm.setFieldsValue({
      name: category.name,
      code: category.code,
      description: category.description
    });
    setCategoryModalOpen(true);
  };

  // 显示创建区域模态框
  const showCreateRegionModal = () => {
    setEditingItem(null);
    regionForm.resetFields();
    setRegionModalOpen(true);
  };

  // 显示编辑区域模态框
  const showEditRegionModal = (region) => {
    setEditingItem(region);
    regionForm.setFieldsValue({
      name: region.name,
      code: region.code,
      description: region.description,
      parent_id: region.parent_id
    });
    setRegionModalOpen(true);
  };

  // 地址解析获取经纬度
  const handleGeocodeAddress = async () => {
    const address = storeForm.getFieldValue('address');
    if (!address) {
      message.warning('请先输入门店地址');
      return;
    }

    setGeocoding(true);
    try {
      const response = await project.store.geocode({ address });
      if (response.success) {
        const { longitude, latitude, formatted_address } = response.data;
        storeForm.setFieldsValue({
          longitude: longitude,
          latitude: latitude,
          address: formatted_address || address
        });
        message.success('地址解析成功');
      } else {
        message.error('地址解析失败');
      }
    } catch (error) {
      console.error('地址解析失败:', error);
      message.error('地址解析失败: ' + (error.message || '未知错误'));
    } finally {
      setGeocoding(false);
    }
  };

  // 处理门店表单提交
  const handleStoreSubmit = async () => {
    try {
      const values = await storeForm.validateFields();
      setLoading(true);
      // 组装 payload，manager_user_id 用 manager 字段的值（如果有）
      const payload = {
        ...values,
        manager_user_id: values.manager || null,
      };
      delete payload.manager; // 不再传 manager 字段
      try {
        if (editingItem) {
          // 更新门店
          await project.store.update(editingItem.id, payload);
          message.success('门店更新成功');
          fetchStores();
        } else {
          // 创建门店
          await project.store.create(payload);
          message.success('门店创建成功');
          fetchStores();
        }
        setStoreModalOpen(false);
      } catch (error) {
        console.error('保存门店失败:', error);
        message.error('保存门店失败: ' + (error.message || '未知错误'));
      } finally {
        setLoading(false);
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理分类表单提交
  const handleCategorySubmit = async () => {
    try {
      const values = await categoryForm.validateFields();
      setLoading(true);

      try {
        if (editingItem) {
          // 更新分类
          await project.store.updateCategory(editingItem.id, values);
          message.success('分类更新成功');
        } else {
          // 创建分类
          // 确保有code字段，如果没有则使用name的拼音首字母
          if (!values.code) {
            values.code = values.name.replace(/\s+/g, '').substring(0, 10);
          }
          await project.store.createCategory(values);
          message.success('分类创建成功');
        }

        // 刷新分类列表
        fetchCategories();
        setCategoryModalOpen(false);
      } catch (error) {
        console.error('保存分类失败:', error);
        message.error('保存分类失败: ' + (error.message || '未知错误'));
      } finally {
        setLoading(false);
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理区域表单提交
  const handleRegionSubmit = async () => {
    try {
      const values = await regionForm.validateFields();
      setLoading(true);

      try {
        if (editingItem) {
          // 更新区域
          await project.store.updateRegion(editingItem.id, values);
          message.success('区域更新成功');
        } else {
          // 创建区域
          // 确保有code字段，如果没有则使用name的拼音首字母
          if (!values.code) {
            values.code = values.name.replace(/\s+/g, '').substring(0, 10);
          }
          await project.store.createRegion(values);
          message.success('区域创建成功');
        }

        // 刷新区域列表
        fetchRegions();
        setRegionModalOpen(false);
      } catch (error) {
        console.error('保存区域失败:', error);
        message.error('保存区域失败: ' + (error.message || '未知错误'));
      } finally {
        setLoading(false);
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理删除门店
  const handleDeleteStore = async (id) => {
    setLoading(true);

    try {
      // 调用API删除门店
      await project.store.delete(id);
      message.success('门店删除成功');

      // 刷新门店列表
      fetchStores();
    } catch (error) {
      console.error('删除门店失败:', error);
      message.error('删除门店失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 处理删除分类
  const handleDeleteCategory = async (id) => {
    setLoading(true);

    try {
      // 调用API删除分类
      await project.store.deleteCategory(id);
      message.success('分类删除成功');

      // 刷新分类列表
      fetchCategories();
    } catch (error) {
      console.error('删除分类失败:', error);
      message.error('删除分类失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 处理删除区域
  const handleDeleteRegion = async (id) => {
    setLoading(true);

    try {
      // 调用API删除区域
      await project.store.deleteRegion(id);
      message.success('区域删除成功');

      // 刷新区域列表
      fetchRegions();
    } catch (error) {
      console.error('删除区域失败:', error);
      message.error('删除区域失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 定义标签页内容
  const tabItems = [
    {
      key: 'stores',
      label: (
        <span>
          <ShopOutlined />
          门店管理
        </span>
      ),
      children: (
        <>
          <div style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showCreateStoreModal}
            >
              新增门店
            </Button>
          </div>
          <Table
            columns={storeColumns}
            dataSource={stores}
            rowKey="id"
            loading={loading}
            pagination={{ pageSize: 10 }}
          />
        </>
      )
    },
    {
      key: 'categories',
      label: (
        <span>
          <AppstoreOutlined />
          门店分类
        </span>
      ),
      children: (
        <>
          <div style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showCreateCategoryModal}
            >
              新增分类
            </Button>
          </div>
          <Table
            columns={categoryColumns}
            dataSource={categories}
            rowKey="id"
            loading={loading}
            pagination={{ pageSize: 10 }}
          />
        </>
      )
    },
    {
      key: 'regions',
      label: (
        <span>
          <EnvironmentOutlined />
          区域管理
        </span>
      ),
      children: (
        <>
          <div style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showCreateRegionModal}
            >
              新增区域
            </Button>
          </div>
          <Table
            columns={regionColumns}
            dataSource={regions}
            rowKey="id"
            loading={loading}
            pagination={{ pageSize: 10 }}
          />
        </>
      )
    }
  ];

  return (
    <div className="store-management">
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
      />

      {/* 门店表单模态框 */}
      <Modal
        title={editingItem ? '编辑门店' : '新增门店'}
        open={storeModalOpen}
        onOk={handleStoreSubmit}
        onCancel={() => setStoreModalOpen(false)}
        confirmLoading={loading}
        width={700}
      >
        <Form
          form={storeForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="门店名称"
                rules={[{ required: true, message: '请输入门店名称' }]}
              >
                <Input placeholder="请输入门店名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label="门店编码"
                rules={[{ required: true, message: '请输入门店编码' }]}
              >
                <Input placeholder="请输入门店编码" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="category_id"
                label="门店分类"
                rules={[{ required: true, message: '请选择门店分类' }]}
              >
                <Select placeholder="请选择门店分类">
                  {categories.map(category => (
                    <Option key={category.id} value={category.id}>{category.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="region_id"
                label="所属区域"
                rules={[{ required: true, message: '请选择所属区域' }]}
              >
                <Select placeholder="请选择所属区域">
                  {regions.map(region => (
                    <Option key={region.id} value={region.id}>{region.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="address"
            label="门店地址"
            rules={[{ required: true, message: '请输入门店地址' }]}
          >
            <Input.Group compact>
              <Input
                style={{ width: 'calc(100% - 100px)' }}
                placeholder="请输入门店地址"
                onChange={(e) => storeForm.setFieldsValue({ address: e.target.value })}
              />
              <Button
                style={{ width: '100px' }}
                type="primary"
                onClick={handleGeocodeAddress}
                loading={geocoding}
              >
                获取坐标
              </Button>
            </Input.Group>
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="longitude"
                label="经度"
              >
                <Input placeholder="自动获取" disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="latitude"
                label="纬度"
              >
                <Input placeholder="自动获取" disabled />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="manager"
                label="负责人"
                rules={[{ required: false, message: '请选择负责人' }]}
                tooltip="可选，创建门店后可以再指定负责人"
              >
                <Select 
                  placeholder="请选择负责人（可选）" 
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  notFoundContent={storeAdmins.length === 0 ? "暂无可选管理员" : "未找到匹配项"}
                >
                  {storeAdmins.map(admin => (
                    <Option key={admin.id} value={String(admin.id)}>
                      {admin.name || admin.username}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="联系电话"
                rules={[{ required: true, message: '请输入联系电话' }]}
              >
                <Input placeholder="请输入联系电话" />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="status"
            label="状态"
            initialValue="active"
          >
            <Select>
              <Option value="active">营业中</Option>
              <Option value="inactive">已关闭</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 分类表单模态框 */}
      <Modal
        title={editingItem ? '编辑分类' : '新增分类'}
        open={categoryModalOpen}
        onOk={handleCategorySubmit}
        onCancel={() => setCategoryModalOpen(false)}
        confirmLoading={loading}
      >
        <Form
          form={categoryForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="分类名称"
                rules={[{ required: true, message: '请输入分类名称' }]}
              >
                <Input placeholder="请输入分类名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label="分类编码"
                rules={[{ required: true, message: '请输入分类编码' }]}
              >
                <Input placeholder="请输入分类编码" />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="description"
            label="分类描述"
          >
            <Input.TextArea placeholder="请输入分类描述" rows={3} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 区域表单模态框 */}
      <Modal
        title={editingItem ? '编辑区域' : '新增区域'}
        open={regionModalOpen}
        onOk={handleRegionSubmit}
        onCancel={() => setRegionModalOpen(false)}
        confirmLoading={loading}
      >
        <Form
          form={regionForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="区域名称"
                rules={[{ required: true, message: '请输入区域名称' }]}
              >
                <Input placeholder="请输入区域名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label="区域编码"
                rules={[{ required: true, message: '请输入区域编码' }]}
              >
                <Input placeholder="请输入区域编码" />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="parent_id"
            label="上级区域"
          >
            <Select
              placeholder="请选择上级区域（可选）"
              allowClear
            >
              {regions.map(region => (
                // 编辑时排除自己，防止循环引用
                (!editingItem || region.id !== editingItem.id) && (
                  <Select.Option key={region.id} value={region.id}>{region.name}</Select.Option>
                )
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="description"
            label="区域描述"
          >
            <Input.TextArea placeholder="请输入区域描述" rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default StoreManagement;
