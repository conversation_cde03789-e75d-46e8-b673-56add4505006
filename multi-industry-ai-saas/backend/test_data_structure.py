#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
from sqlalchemy import text
from db.database import AsyncSessionLocal

async def test_api():
    async with AsyncSessionLocal() as db:
        # 测试门店KPI API返回的数据结构
        store_id = '2a8f1928-0d76-46cf-b87b-e83c6e1cc966'
        query_date = '2025-06-19'
        
        # 模拟KPI API的简化查询
        target_sql = text('SELECT target_amount FROM store_sales_targets WHERE store_id = :store_id LIMIT 1')
        target_result = await db.execute(target_sql, {'store_id': store_id})
        target_row = target_result.first()
        monthly_target = float(target_row[0]) if target_row else 150000.0
        
        sales_sql = text('SELECT COALESCE(SUM(total_sales), 0) FROM sales_reports WHERE store_id = :store_id AND DATE(report_date) = :query_date')
        sales_result = await db.execute(sales_sql, {'store_id': store_id, 'query_date': query_date})
        actual_sales = float(sales_result.scalar() or 0)
        
        print('🔧 后端返回的数据结构:')
        backend_data = {
            'success': True,
            'data': {
                'sales': {'value': actual_sales, 'yoy': '+12.5%', 'mom': '+5.8%'},
                'transactions': {'value': 456, 'yoy': '+8.2%', 'mom': '+3.1%'},
                'avg_transaction_value': {'value': 28.2, 'yoy': '+4.1%', 'mom': '+2.6%'},
                'loss_rate': {'value': 2.3, 'yoy': '-0.5%', 'mom': '-0.2%'},
                'sales_target': {'total': monthly_target, 'completed': actual_sales}
            }
        }
        print(backend_data)
        
        print('\n🎯 前端期望的转换结构:')
        frontend_expected = {
            'totalRevenue': backend_data['data']['sales'],
            'totalLoss': backend_data['data']['loss_rate'],  # 🚨 这里有问题！
            'customerTraffic': backend_data['data']['transactions'],
            'salesTarget': backend_data['data']['sales_target']
        }
        print(frontend_expected)
        
        print('\n❌ 问题分析:')
        print('1. 前端期望 totalLoss，但后端返回 loss_rate')
        print('2. 前端解析的字段名和后端返回的字段名不匹配')

if __name__ == "__main__":
    asyncio.run(test_api()) 